"use server";

import { z } from "zod";
import { PurchaseSchema } from "@/schemas/zod";
import { db } from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { createPurchaseSuccessNotification } from "@/lib/create-system-notification";
import { generatePurchaseId } from "@/lib/generate-id";

// Function to get the next transaction number with global uniqueness and custom number filtering
export const getNextTransactionNumber = async (
  prefix: string,
  date?: Date | string
) => {
  try {
    // Use provided date or current date
    const baseDate = date ? new Date(date) : new Date();
    const year = baseDate.getFullYear();
    const yearSuffix = String(year).slice(-2); // Get last 2 digits of year

    // Determine the new prefix and regex pattern based on the input prefix
    let newPrefix: string;
    let regexPattern: RegExp;

    if (prefix.toUpperCase() === "TRX") {
      // For purchase transactions: BELI-{YY}B{NNNNNN}
      newPrefix = "BELI";
      // Regex to match only auto-generated sequential numbers: BELI-25B000001, BELI-25B000002, etc.
      regexPattern = new RegExp(`^BELI-${yearSuffix}B\\d{6}$`);
    } else if (prefix.toUpperCase() === "INV") {
      // For invoice references: INV-{YY}B{NNNNNN}
      newPrefix = "INV";
      // Regex to match only auto-generated sequential numbers: INV-25B000001, INV-25B000002, etc.
      regexPattern = new RegExp(`^INV-${yearSuffix}B\\d{6}$`);
    } else {
      // For other prefixes, keep the old behavior
      newPrefix = prefix.toUpperCase();
      regexPattern = new RegExp(
        `^${prefix.toUpperCase()}-${yearSuffix}B\\d{6}$`
      );
    }

    // Count GLOBALLY across all users for auto-generated transaction numbers only
    // This ensures global uniqueness and excludes custom/manual entries
    if (prefix.toUpperCase() === "TRX") {
      // Get all purchase transaction numbers that match the auto-generated pattern
      const purchasesWithAutoTrx = await db.purchase.findMany({
        where: {
          transactionNumber: {
            startsWith: `BELI-${yearSuffix}B`,
          },
        },
        select: {
          transactionNumber: true,
        },
      });

      // Filter to only count auto-generated sequential numbers
      const autoGeneratedCount = purchasesWithAutoTrx.filter(
        (purchase) =>
          purchase.transactionNumber &&
          regexPattern.test(purchase.transactionNumber)
      ).length;

      // Calculate the next number (add 1 to the current count)
      const nextNumber = autoGeneratedCount + 1;

      // New format: BELI-25B000001
      const formattedNumber = String(nextNumber).padStart(6, "0");
      return `${newPrefix}-${yearSuffix}B${formattedNumber}`;
    } else if (prefix.toUpperCase() === "INV") {
      // For invoice references, we need to ensure global uniqueness across both sales and purchases
      // Get all purchase invoice references that match the auto-generated pattern
      const purchasesWithAutoInv = await db.purchase.findMany({
        where: {
          invoiceRef: {
            startsWith: `INV-${yearSuffix}B`,
          },
        },
        select: {
          invoiceRef: true,
        },
      });

      // Get all sales invoice references that match the auto-generated pattern
      const salesWithAutoInv = await db.sale.findMany({
        where: {
          invoiceRef: {
            startsWith: `INV-${yearSuffix}`,
          },
        },
        select: {
          invoiceRef: true,
        },
      });

      // Combine and filter to only count auto-generated sequential numbers
      const globalInvoiceRegex = new RegExp(`^INV-${yearSuffix}[JB]\\d{6}$`);
      const allAutoInvoices = [
        ...purchasesWithAutoInv.map((p) => p.invoiceRef),
        ...salesWithAutoInv.map((s) => s.invoiceRef),
      ].filter(
        (invoiceRef) => invoiceRef && globalInvoiceRegex.test(invoiceRef)
      );

      // Calculate the next number (add 1 to the current count)
      const nextNumber = allAutoInvoices.length + 1;

      // New format: INV-25B000001 (B for purchases)
      const formattedNumber = String(nextNumber).padStart(6, "0");
      return `${newPrefix}-${yearSuffix}B${formattedNumber}`;
    } else {
      // Keep old format for other prefixes
      const purchasesWithOldTrx = await db.purchase.findMany({
        where: {
          transactionNumber: {
            startsWith: `${prefix.toUpperCase()}-${yearSuffix}`,
          },
        },
        select: {
          transactionNumber: true,
        },
      });

      const autoGeneratedCount = purchasesWithOldTrx.filter(
        (purchase) =>
          purchase.transactionNumber &&
          regexPattern.test(purchase.transactionNumber)
      ).length;

      const nextNumber = autoGeneratedCount + 1;
      const formattedNumber = `B${String(nextNumber).padStart(6, "0")}`;
      return `${prefix.toUpperCase()}-${yearSuffix}${formattedNumber}`;
    }
  } catch (error) {
    console.error("Error generating next transaction number:", error);

    // Get current year for fallback
    const fallbackDate = date ? new Date(date) : new Date();
    const year = fallbackDate.getFullYear();
    const yearSuffix = String(year).slice(-2);

    // Fallback based on prefix
    if (prefix.toUpperCase() === "TRX") {
      return `BELI-${yearSuffix}B000001`;
    } else if (prefix.toUpperCase() === "INV") {
      return `INV-${yearSuffix}B000001`;
    } else {
      return `${prefix.toUpperCase()}-${yearSuffix}B000001`;
    }
  }
};

export const addPurchase = async (values: z.infer<typeof PurchaseSchema>) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Validate input server-side
  const validatedFields = PurchaseSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const {
    items,
    totalAmount,
    invoiceRef,
    supplierId: rawSupplierId,
    isDraft,
    supplierEmail,
    transactionDate,
    paymentDueDate,
    transactionNumber,
    tags,
    billingAddress,
    warehouse, // This is used in the UI but not stored in the database
    memo,
    lampiran,
  } = validatedFields.data;

  // Ensure supplierId is null if it's an empty string or nullish, otherwise use the ID
  const supplierId = rawSupplierId ? rawSupplierId : null;

  try {
    // 2. Create purchase in database with transaction to ensure all operations succeed or fail together
    const result = await db.$transaction(async (tx) => {
      // Generate a custom purchase ID with company-specific sequence
      const customPurchaseId = await generatePurchaseId(userId);

      // Create the purchase record with custom ID
      const purchase = await tx.purchase.create({
        data: {
          id: customPurchaseId, // Use the custom ID
          totalAmount,
          invoiceRef,
          userId,
          supplierId,
          isDraft: isDraft || false,
          // New fields
          supplierEmail,
          transactionDate,
          paymentDueDate,
          transactionNumber: transactionNumber || customPurchaseId, // Use custom ID as transaction number if not provided
          tags: tags || [],
          billingAddress,

          memo: memo || "",
          // Store lampiran as JSON objects directly
          lampiran: lampiran || [],
          items: {
            create: items.map((item) => ({
              quantity: item.quantity,
              costAtPurchase: item.costAtPurchase,
              productId: item.productId,
              unit: item.unit || "Buah",
              tax: item.tax || null,
            })),
          },
        },
        include: {
          items: true,
        },
      });

      // Update product stock for each item purchased (only if not a draft)
      if (!isDraft) {
        for (const item of items) {
          await tx.product.update({
            where: { id: item.productId },
            data: {
              stock: {
                increment: item.quantity,
              },
            },
          });
        }
      }

      return purchase;
    });

    // 3. Create a notification for the purchase
    await createPurchaseSuccessNotification(
      result.id,
      result.totalAmount.toNumber()
    );

    // 4. Revalidate the purchases page cache
    revalidatePath("/dashboard/purchases");

    // Get the updated purchase with items
    const updatedPurchase = await db.purchase.findUnique({
      where: { id: result.id },
      include: { items: true },
    });

    if (!updatedPurchase) {
      return { error: "Gagal mengambil data pembelian yang dibuat." };
    }

    // Serialize the result to convert Decimal to number and Date to string
    const serializedResult = {
      id: updatedPurchase.id,
      purchaseDate: updatedPurchase.purchaseDate.toISOString(),
      totalAmount: updatedPurchase.totalAmount.toNumber(),
      invoiceRef: updatedPurchase.invoiceRef,
      isDraft: updatedPurchase.isDraft,
      createdAt: updatedPurchase.createdAt.toISOString(),
      updatedAt: updatedPurchase.updatedAt.toISOString(),
      userId: updatedPurchase.userId,
      supplierId: updatedPurchase.supplierId,
      // New fields
      supplierEmail: updatedPurchase.supplierEmail,
      transactionDate: updatedPurchase.transactionDate?.toISOString(),
      paymentDueDate: updatedPurchase.paymentDueDate?.toISOString(),
      transactionNumber: updatedPurchase.transactionNumber,
      tags: updatedPurchase.tags,
      billingAddress: updatedPurchase.billingAddress,

      memo: updatedPurchase.memo,
      // Use lampiran directly as it's already JSON objects
      lampiran: updatedPurchase.lampiran || [],
      warehouse: warehouse, // Include the warehouse field from the form data
      items: updatedPurchase.items.map((item) => ({
        id: item.id,
        quantity: item.quantity,
        costAtPurchase: item.costAtPurchase.toNumber(),
        purchaseId: item.purchaseId,
        productId: item.productId,
        unit: item.unit,
        tax: item.tax,
        createdAt: item.createdAt.toISOString(),
        updatedAt: item.updatedAt.toISOString(),
      })),
    };

    return {
      success: "Pembelian berhasil dicatat!",
      data: serializedResult,
    };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal mencatat pembelian. Silakan coba lagi." };
  }
};

export const getPurchases = async () => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    const purchases = await db.purchase.findMany({
      where: {
        userId,
      },
      orderBy: {
        purchaseDate: "desc",
      },
      include: {
        items: {
          include: {
            product: {
              select: {
                name: true,
              },
            },
          },
        },
        supplier: true,
      },
    });

    // Convert Decimal to number and Date to string for serialization
    const serializedPurchases = purchases.map((purchase) => {
      // Explicitly create a new plain object
      const plainPurchase: any = {
        id: purchase.id,
        purchaseDate: purchase.purchaseDate.toISOString(), // Convert Date to ISO string
        totalAmount: purchase.totalAmount.toNumber(), // Convert Decimal to number
        invoiceRef: purchase.invoiceRef,
        isDraft: purchase.isDraft,
        createdAt: purchase.createdAt.toISOString(), // Convert Date to ISO string
        updatedAt: purchase.updatedAt.toISOString(), // Convert Date to ISO string
        userId: purchase.userId,
        supplierId: purchase.supplierId,
        // New fields
        supplierEmail: purchase.supplierEmail,
        transactionDate: purchase.transactionDate?.toISOString(),
        paymentDueDate: purchase.paymentDueDate?.toISOString(),
        transactionNumber: purchase.transactionNumber,
        tags: purchase.tags,
        billingAddress: purchase.billingAddress,

        memo: purchase.memo,
        // Use lampiran directly as it's already JSON objects
        lampiran: purchase.lampiran || [],
        warehouse: null, // We don't store this in the database, so it's null when fetching
        // Explicitly handle supplier (it's already plain or null)
        supplier: purchase.supplier
          ? {
              id: purchase.supplier.id,
              name: purchase.supplier.name,
            }
          : null,
        items: purchase.items.map((item) => ({
          id: item.id,
          quantity: item.quantity,
          costAtPurchase: item.costAtPurchase.toNumber(), // Convert Decimal to number
          purchaseId: item.purchaseId,
          productId: item.productId,
          createdAt: item.createdAt.toISOString(), // Convert Date to ISO string
          updatedAt: item.updatedAt.toISOString(), // Convert Date to ISO string
          // Explicitly handle product name (already plain)
          product: {
            name: item.product.name,
          },
        })),
      };
      return plainPurchase;
    });

    return { purchases: serializedPurchases };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal mengambil data pembelian." };
  }
};

export const getPurchaseById = async (id: string) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // First try to find by transaction number (case-insensitive)
    let purchase = await db.purchase.findFirst({
      where: {
        transactionNumber: {
          mode: "insensitive",
          equals: id,
        },
        userId, // Ensure the purchase belongs to the current user
      },
      include: {
        items: {
          include: {
            product: true,
          },
        },
        supplier: true,
      },
    });

    // If not found by transaction number, check if the ID starts with "pur-" (case-insensitive)
    if (!purchase && id.toLowerCase().startsWith("pur-")) {
      purchase = await db.purchase.findFirst({
        where: {
          id: {
            mode: "insensitive",
            equals: id,
          },
          userId, // Ensure the purchase belongs to the current user
        },
        include: {
          items: {
            include: {
              product: true,
            },
          },
          supplier: true,
        },
      });
    }

    // If still not found, try to find by exact ID
    if (!purchase) {
      purchase = await db.purchase.findUnique({
        where: {
          id,
          userId, // Ensure the purchase belongs to the current user
        },
        include: {
          items: {
            include: {
              product: true,
            },
          },
          supplier: true,
        },
      });
    }

    if (!purchase) {
      return { error: "Pembelian tidak ditemukan." };
    }

    // Convert Decimal to number for serialization
    const serializedPurchase = {
      ...purchase,
      totalAmount: Number(purchase.totalAmount),
      isDraft: purchase.isDraft,
      // Ensure dates are properly serialized
      purchaseDate: purchase.purchaseDate.toISOString(),
      createdAt: purchase.createdAt.toISOString(),
      updatedAt: purchase.updatedAt.toISOString(),
      transactionDate: purchase.transactionDate?.toISOString(),
      paymentDueDate: purchase.paymentDueDate?.toISOString(),
      // Ensure additional fields are included
      memo: purchase.memo || "",
      // Use lampiran directly as it's already JSON objects
      lampiran: purchase.lampiran || [],
      items: purchase.items.map((item) => ({
        ...item,
        costAtPurchase: Number(item.costAtPurchase),
        createdAt: item.createdAt.toISOString(),
        updatedAt: item.updatedAt.toISOString(),
        product: {
          ...item.product,
          price: Number(item.product.price),
          cost: item.product.cost ? Number(item.product.cost) : null,
          createdAt: item.product.createdAt.toISOString(),
          updatedAt: item.product.updatedAt.toISOString(),
          // Convert all other Decimal fields to numbers
          weight: item.product.weight ? Number(item.product.weight) : null,
          length: item.product.length ? Number(item.product.length) : null,
          width: item.product.width ? Number(item.product.width) : null,
          height: item.product.height ? Number(item.product.height) : null,
          wholesalePrice: item.product.wholesalePrice
            ? Number(item.product.wholesalePrice)
            : null,
          // Convert tax rate Decimal fields to numbers
          salePriceTaxRate: item.product.salePriceTaxRate
            ? Number(item.product.salePriceTaxRate)
            : null,
          wholesalePriceTaxRate: item.product.wholesalePriceTaxRate
            ? Number(item.product.wholesalePriceTaxRate)
            : null,
          costPriceTaxRate: item.product.costPriceTaxRate
            ? Number(item.product.costPriceTaxRate)
            : null,
        },
      })),
    };

    return { purchase: serializedPurchase };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal mengambil detail pembelian." };
  }
};

export const updatePurchase = async (
  id: string,
  values: z.infer<typeof PurchaseSchema>
) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  // 1. Validate input server-side
  const validatedFields = PurchaseSchema.safeParse(values);

  if (!validatedFields.success) {
    console.error(
      "Validation Error:",
      validatedFields.error.flatten().fieldErrors
    );
    return { error: "Input tidak valid!" };
  }

  const {
    items,
    totalAmount,
    invoiceRef,
    supplierId: rawSupplierId,
    isDraft,
    supplierEmail,
    transactionDate,
    paymentDueDate,
    transactionNumber,
    tags,
    billingAddress,
    warehouse, // This is used in the UI but not stored in the database
    memo,
    lampiran,
  } = validatedFields.data;

  // Ensure supplierId is null if it's an empty string or nullish, otherwise use the ID
  const supplierId = rawSupplierId ? rawSupplierId : null;

  try {
    // First, try to find by transaction number (case-insensitive)
    let existingPurchase = await db.purchase.findFirst({
      where: {
        transactionNumber: {
          mode: "insensitive",
          equals: id,
        },
        userId, // Ensure the purchase belongs to the current user
      },
      include: {
        items: true,
      },
    });

    // If not found by transaction number, check if the ID starts with "pur-" (case-insensitive)
    if (!existingPurchase && id.toLowerCase().startsWith("pur-")) {
      existingPurchase = await db.purchase.findFirst({
        where: {
          id: {
            mode: "insensitive",
            equals: id,
          },
          userId, // Ensure the purchase belongs to the current user
        },
        include: {
          items: true,
        },
      });
    }

    // If still not found, try to find by exact ID
    if (!existingPurchase) {
      existingPurchase = await db.purchase.findUnique({
        where: {
          id,
          userId,
        },
        include: {
          items: true,
        },
      });
    }

    if (!existingPurchase) {
      return { error: "Pembelian tidak ditemukan!" };
    }

    // Get the original items to calculate stock adjustments
    const originalItems = existingPurchase.items;

    // 2. Update purchase in database with transaction to ensure all operations succeed or fail together
    await db.$transaction(async (tx) => {
      // First, delete all existing items
      await tx.purchaseItem.deleteMany({
        where: {
          purchaseId: existingPurchase.id,
        },
      });

      // Update the purchase record
      const purchase = await tx.purchase.update({
        where: {
          id: existingPurchase.id,
        },
        data: {
          totalAmount,
          invoiceRef,
          supplierId,
          isDraft: isDraft || false,
          // New fields
          supplierEmail,
          transactionDate,
          paymentDueDate,
          transactionNumber,
          tags: tags || [],
          billingAddress,

          memo: memo || "",
          // Store lampiran as JSON objects directly
          lampiran: lampiran || [],
          items: {
            create: items.map((item) => ({
              quantity: item.quantity,
              costAtPurchase: item.costAtPurchase,
              productId: item.productId,
              unit: item.unit || "Buah",
              tax: item.tax || null,
            })),
          },
        },
        include: {
          items: true,
        },
      });

      // Adjust product stock: first subtract the original quantities, then add the new quantities
      // This handles both new items, removed items, and quantity changes

      // Only update stock if neither the original nor the updated purchase is a draft
      if (!existingPurchase.isDraft && !isDraft) {
        // Subtract original quantities (reverse the original purchase)
        for (const item of originalItems) {
          await tx.product.update({
            where: { id: item.productId },
            data: {
              stock: {
                decrement: item.quantity,
              },
            },
          });
        }

        // Add new quantities (apply the updated purchase)
        for (const item of items) {
          await tx.product.update({
            where: { id: item.productId },
            data: {
              stock: {
                increment: item.quantity,
              },
            },
          });
        }
      }
      // If original was a draft but new one isn't, add the quantities
      else if (existingPurchase.isDraft && !isDraft) {
        // Add new quantities (apply the updated purchase)
        for (const item of items) {
          await tx.product.update({
            where: { id: item.productId },
            data: {
              stock: {
                increment: item.quantity,
              },
            },
          });
        }
      }
      // If original wasn't a draft but new one is, subtract the quantities
      else if (!existingPurchase.isDraft && isDraft) {
        // Subtract original quantities (reverse the original purchase)
        for (const item of originalItems) {
          await tx.product.update({
            where: { id: item.productId },
            data: {
              stock: {
                decrement: item.quantity,
              },
            },
          });
        }
      }
      // If both are drafts, don't update stock

      return purchase;
    });

    // 3. Revalidate the purchases page cache
    revalidatePath("/dashboard/purchases");
    revalidatePath(`/dashboard/purchases/${id}`);

    // Get the updated purchase with items
    const updatedPurchase = await db.purchase.findUnique({
      where: { id: existingPurchase.id },
      include: { items: true },
    });

    if (!updatedPurchase) {
      return { error: "Gagal mengambil data pembelian yang diperbarui." };
    }

    // Serialize the result to convert Decimal to number and Date to string
    const serializedResult = {
      id: updatedPurchase.id,
      purchaseDate: updatedPurchase.purchaseDate.toISOString(),
      totalAmount: updatedPurchase.totalAmount.toNumber(),
      invoiceRef: updatedPurchase.invoiceRef,
      isDraft: updatedPurchase.isDraft,
      createdAt: updatedPurchase.createdAt.toISOString(),
      updatedAt: updatedPurchase.updatedAt.toISOString(),
      userId: updatedPurchase.userId,
      supplierId: updatedPurchase.supplierId,
      // New fields
      supplierEmail: updatedPurchase.supplierEmail,
      transactionDate: updatedPurchase.transactionDate?.toISOString(),
      paymentDueDate: updatedPurchase.paymentDueDate?.toISOString(),
      transactionNumber: updatedPurchase.transactionNumber,
      tags: updatedPurchase.tags,
      billingAddress: updatedPurchase.billingAddress,
      // Additional fields
      memo: updatedPurchase.memo,
      // Use lampiran directly as it's already JSON objects
      lampiran: updatedPurchase.lampiran || [],
      warehouse: warehouse, // Include the warehouse field from the form data
      items: updatedPurchase.items.map((item) => ({
        id: item.id,
        quantity: item.quantity,
        costAtPurchase: item.costAtPurchase.toNumber(),
        purchaseId: item.purchaseId,
        productId: item.productId,
        unit: item.unit,
        tax: item.tax,
        createdAt: item.createdAt.toISOString(),
        updatedAt: item.updatedAt.toISOString(),
      })),
    };

    return {
      success: "Pembelian berhasil diperbarui!",
      data: serializedResult,
    };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal memperbarui pembelian. Silakan coba lagi." };
  }
};

export const deletePurchase = async (id: string) => {
  // Get effective user ID (owner ID if employee, user's own ID otherwise)
  const effectiveUserId = await getEffectiveUserId();

  if (!effectiveUserId) {
    return { error: "Tidak terautentikasi!" };
  }
  const userId = effectiveUserId;

  try {
    // First, try to find by transaction number (case-insensitive)
    let existingPurchase = await db.purchase.findFirst({
      where: {
        transactionNumber: {
          mode: "insensitive",
          equals: id,
        },
        userId, // Ensure the purchase belongs to the current user
      },
      include: {
        items: true,
      },
    });

    // If not found by transaction number, check if the ID starts with "pur-" (case-insensitive)
    if (!existingPurchase && id.toLowerCase().startsWith("pur-")) {
      existingPurchase = await db.purchase.findFirst({
        where: {
          id: {
            mode: "insensitive",
            equals: id,
          },
          userId, // Ensure the purchase belongs to the current user
        },
        include: {
          items: true,
        },
      });
    }

    // If still not found, try to find by exact ID
    if (!existingPurchase) {
      existingPurchase = await db.purchase.findUnique({
        where: {
          id,
          userId,
        },
        include: {
          items: true,
        },
      });
    }

    if (!existingPurchase) {
      return { error: "Pembelian tidak ditemukan!" };
    }

    // Get the original items to revert stock changes
    const originalItems = existingPurchase.items;

    // Use a transaction to ensure all operations succeed or fail together
    await db.$transaction(async (tx) => {
      // Only revert stock changes if the purchase is not a draft
      if (!existingPurchase.isDraft) {
        // Revert the stock changes by decrementing the stock for each item
        for (const item of originalItems) {
          await tx.product.update({
            where: { id: item.productId },
            data: {
              stock: {
                decrement: item.quantity,
              },
            },
          });
        }
      }

      // Delete all purchase items
      await tx.purchaseItem.deleteMany({
        where: {
          purchaseId: existingPurchase.id,
        },
      });

      // Delete the purchase
      await tx.purchase.delete({
        where: {
          id: existingPurchase.id,
          userId, // Ensure the purchase belongs to the current user
        },
      });
    });

    // Revalidate the purchases page cache
    revalidatePath("/dashboard/purchases");

    return { success: "Pembelian berhasil dihapus!" };
  } catch (error) {
    console.error("Database Error:", error);
    return { error: "Gagal menghapus pembelian. Silakan coba lagi." };
  }
};
