"use client";

import React, { useState, useEffect, useTransition } from "react";
import { useRouter } from "next/navigation";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import {
  updatePurchase,
  getNextTransactionNumber,
} from "@/actions/entities/purchases";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import {
  PurchaseFormValues,
  Product,
  Supplier,
  EnhancedPurchaseSchema,
} from "../new/types";
import CombinedPurchaseForm from "../new/components/CombinedPurchaseForm";
import { ArrowLeft, Check, Save } from "lucide-react";

// Define the Purchase interface
interface PurchaseItem {
  id: string;
  quantity: number;
  costAtPurchase: number;
  productId: string;
  product: Product;
  unit?: string;
  tax?: string | null;
  discountPercentage?: number | null;
  discountAmount?: number | null;
}

interface Purchase {
  id: string;
  totalAmount: number;
  invoiceRef: string | null;
  purchaseDate: string;
  supplierId: string | null;
  items: PurchaseItem[];
  isDraft: boolean;
  supplierEmail?: string | null;
  transactionDate?: string;
  paymentDueDate?: string | null;
  transactionNumber?: string | null;
  tags?: string[];
  billingAddress?: string | null;
  // Warehouse relationship
  warehouseId?: string | null;
  warehouse?: {
    id: string;
    name: string;
  } | null;

  memo?: string | null;
  lampiran?: Array<{ url: string; filename: string }>;
  createdAt: string; // Add createdAt field
}

// Props use imported types
interface EnhancedPurchaseEditPageProps {
  purchase: Purchase;
  products: Product[];
  suppliers: Supplier[];
}

const EnhancedPurchaseEditPage: React.FC<EnhancedPurchaseEditPageProps> = ({
  purchase,
  products,
  suppliers,
}) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [totalAmount, setTotalAmount] = useState<number>(purchase.totalAmount);

  // Initialize the form with enhanced schema and purchase data
  const form = useForm<PurchaseFormValues>({
    resolver: zodResolver(EnhancedPurchaseSchema),
    defaultValues: {
      items: purchase.items.map((item) => ({
        productId: item.productId,
        quantity: item.quantity,
        costAtPurchase: item.costAtPurchase,
        unit: item.unit || "Buah",
        tax: item.tax || "",
      })),
      totalAmount: purchase.totalAmount,
      invoiceRef: purchase.invoiceRef || "",
      supplierId: purchase.supplierId || "",
      paymentStatus: "paid",
      trackDelivery: false,
      notifyOnArrival: false,
      isDraft: false,
      // New fields
      supplierEmail: purchase.supplierEmail || "",
      transactionDate: purchase.transactionDate
        ? new Date(purchase.transactionDate)
        : new Date(),
      paymentDueDate: purchase.paymentDueDate
        ? new Date(purchase.paymentDueDate)
        : undefined,
      transactionNumber: purchase.transactionNumber || "",
      tags: purchase.tags || [],
      billingAddress: purchase.billingAddress || "",
      warehouse: purchase.warehouse || "",

      memo: purchase.memo || "",
      lampiran: purchase.lampiran || [],
      priceIncludesTax: false,
      deliveryDate: undefined,
    },
  });

  // Get the items field array
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Watch for changes in the items array to calculate the total
  const items = form.watch("items");

  // Calculate total amount whenever items change
  useEffect(() => {
    const priceIncludesTax = form.watch("priceIncludesTax");

    const total = items.reduce(
      (sum: number, item: PurchaseFormValues["items"][number]) => {
        // Ensure item and properties exist before calculation
        const quantity = item?.quantity ?? 0;
        const cost = item?.costAtPurchase ?? 0;
        const taxRate = parseFloat(item?.tax || "0") / 100;

        let itemSubtotal;
        if (priceIncludesTax) {
          // If price includes tax, the total is simply quantity * cost
          itemSubtotal = quantity * cost;
        } else {
          // If price doesn't include tax, we add tax to the price
          const subtotalBeforeTax = quantity * cost;
          const taxAmount = subtotalBeforeTax * taxRate;
          itemSubtotal = subtotalBeforeTax + taxAmount;
        }

        return sum + itemSubtotal;
      },
      0
    );
    setTotalAmount(total);
    form.setValue("totalAmount", total);
  }, [items, form]);

  // Handle product selection
  const handleProductChange = (index: number, productId: string) => {
    const selectedProduct: Product | undefined = products.find(
      (p) => p.id === productId
    );
    // Check if selectedProduct exists
    if (selectedProduct) {
      // Set cost if available
      if (typeof selectedProduct.cost === "number") {
        const costValue = selectedProduct.cost; // Assign to variable to help TS inference
        form.setValue(`items.${index}.costAtPurchase`, costValue);
      }

      // Set unit from product
      if (selectedProduct.unit) {
        form.setValue(`items.${index}.unit`, selectedProduct.unit);
      }

      // Force recalculation of total immediately
      const currentItems = form.getValues("items");

      // Ensure the item exists before trying to update it
      if (currentItems[index]) {
        if (selectedProduct.cost) {
          currentItems[index].costAtPurchase = selectedProduct.cost;
        }
        if (selectedProduct.unit) {
          currentItems[index].unit = selectedProduct.unit;
        }
      }

      const priceIncludesTax = form.watch("priceIncludesTax");

      const total = currentItems.reduce(
        (sum: number, item: PurchaseFormValues["items"][number]) => {
          // Ensure item and properties exist before calculation
          const quantity = item?.quantity ?? 0;
          const cost = item?.costAtPurchase ?? 0;
          const taxRate = parseFloat(item?.tax || "0") / 100;

          let itemSubtotal;
          if (priceIncludesTax) {
            // If price includes tax, the total is simply quantity * cost
            itemSubtotal = quantity * cost;
          } else {
            // If price doesn't include tax, we add tax to the price
            const subtotalBeforeTax = quantity * cost;
            const taxAmount = subtotalBeforeTax * taxRate;
            itemSubtotal = subtotalBeforeTax + taxAmount;
          }

          return sum + itemSubtotal;
        },
        0
      );

      setTotalAmount(total);
      form.setValue("totalAmount", total);
    }
  };

  // Handle form submission
  const onSubmit = (values: PurchaseFormValues) => {
    startTransition(async () => {
      try {
        // Generate auto values if needed
        let autoTransactionNumber = values.transactionNumber;
        let autoInvoiceRef = values.invoiceRef;

        // If transaction number is empty, generate one using the purchase's createdAt date
        if (!values.transactionNumber) {
          autoTransactionNumber = await getNextTransactionNumber(
            "TRX",
            purchase.createdAt
          );
        }

        // If invoice reference is empty, generate one using the purchase's createdAt date
        if (!values.invoiceRef) {
          autoInvoiceRef = await getNextTransactionNumber(
            "INV",
            purchase.createdAt
          );
        }

        // Include all the fields for the purchase update
        const purchaseData = {
          items: values.items.map((item) => ({
            productId: item.productId,
            quantity: item.quantity,
            costAtPurchase: item.costAtPurchase,
            unit: item.unit || "Buah",
            tax: item.tax || "",
          })),
          totalAmount: values.totalAmount,
          invoiceRef: autoInvoiceRef,
          supplierId: values.supplierId,
          isDraft: false, // Always set to false when publishing
          // New fields
          supplierEmail: values.supplierEmail || "",
          transactionDate: values.transactionDate || new Date(),
          paymentDueDate: values.paymentDueDate,
          transactionNumber: autoTransactionNumber,
          tags: values.tags || [],
          billingAddress: values.billingAddress || "",
          // Additional fields
          memo: values.memo || "",
          lampiran: values.lampiran || [],
          // Warehouse is not stored in the database
          warehouse: values.warehouse || "",
        };

        const result = await updatePurchase(purchase.id, purchaseData);
        if (result.success) {
          toast.success(result.success);
          // Redirect after a short delay
          router.push(
            `/dashboard/purchases/detail/${autoTransactionNumber || purchase.id}`
          );
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  // Save as draft to database
  const saveAsDraft = () => {
    startTransition(async () => {
      try {
        // Get current form values
        const values = form.getValues();

        // Generate transaction number and invoice reference if needed
        let autoTransactionNumber = values.transactionNumber;
        let autoInvoiceRef = values.invoiceRef;

        if (!autoTransactionNumber) {
          autoTransactionNumber = await getNextTransactionNumber(
            "TRX",
            purchase.createdAt
          );
        }

        if (!autoInvoiceRef) {
          autoInvoiceRef = await getNextTransactionNumber(
            "INV",
            purchase.createdAt
          );
        }

        // Extract the fields for the purchase update (only fields in base PurchaseSchema)
        const purchaseData = {
          items: values.items.map((item) => ({
            productId: item.productId,
            quantity: item.quantity,
            costAtPurchase: item.costAtPurchase,
            unit: item.unit || "Buah",
            tax: item.tax || "",
          })),
          totalAmount: values.totalAmount,
          invoiceRef: autoInvoiceRef,
          supplierId: values.supplierId,
          isDraft: true, // Set to true when saving as draft
          // New fields
          supplierEmail: values.supplierEmail || "",
          transactionDate: values.transactionDate || new Date(),
          paymentDueDate: values.paymentDueDate,
          transactionNumber: autoTransactionNumber,
          tags: values.tags || [],
          billingAddress: values.billingAddress || "",
          // Additional fields
          memo: values.memo || "",
          lampiran: values.lampiran || [],
          // Warehouse is not stored in the database
          warehouse: values.warehouse || "",
        };

        const result = await updatePurchase(purchase.id, purchaseData);
        if (result.success) {
          toast.success("Pembelian berhasil disimpan sebagai draft!");
          // Redirect after a short delay
          router.push(
            `/dashboard/purchases/detail/${autoTransactionNumber || purchase.id}`
          );
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  return (
    <div className="w-full px-4 py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Edit Pembelian</h1>
          <p className="text-muted-foreground">
            Perbarui transaksi pembelian dengan mengisi detail di bawah ini
          </p>
        </div>
        <Button variant="outline" asChild className="gap-2">
          <Link
            href={`/dashboard/purchases/detail/${purchase.transactionNumber || purchase.id}`}
          >
            <ArrowLeft className="h-4 w-4" />
            Kembali
          </Link>
        </Button>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="space-y-6">
            {/* Main Form Content */}
            <CombinedPurchaseForm
              control={form.control}
              isPending={isPending}
              products={products}
              suppliers={suppliers}
              items={items}
              fields={fields}
              append={append}
              remove={remove}
              handleProductChange={handleProductChange}
              totalAmount={totalAmount}
              createdAt={purchase.createdAt}
              setValue={form.setValue}
              trigger={form.trigger}
            />

            {/* Purchase Summary section removed */}
          </div>

          {/* Form Actions */}
          <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              asChild
              disabled={isPending}
            >
              <Link
                href={`/dashboard/purchases/detail/${purchase.transactionNumber || purchase.id}`}
              >
                Batal
              </Link>
            </Button>
            <Button
              type="button"
              variant="secondary"
              disabled={isPending}
              className="gap-2"
              onClick={saveAsDraft}
            >
              <Save className="h-4 w-4" />
              <span>Simpan ke Draft</span>
            </Button>
            <Button type="submit" disabled={isPending} className="gap-2">
              {isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Menyimpan...</span>
                </>
              ) : (
                <>
                  <Check className="h-4 w-4" />
                  <span>Simpan Perubahan</span>
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default EnhancedPurchaseEditPage;
