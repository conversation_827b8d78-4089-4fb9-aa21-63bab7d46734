"use client";

import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { createPortal } from "react-dom";
import {
  Control,
  UseFieldArrayRemove,
  FieldValues,
  useFormContext,
} from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { TrashIcon } from "@heroicons/react/24/outline";
import { ChevronDown, ChevronUp, Plus } from "lucide-react";
import {
  PurchaseFormValues,
  Product,
} from "@/components/pages/dashboard/purchases/new/types";
import { formatCurrency } from "@/lib/utils";

interface CustomDropdownProductSelectProps {
  control: Control<PurchaseFormValues>;
  index: number;
  field: FieldValues; // Used by useFieldArray, even if not directly referenced
  products: Product[];
  items: PurchaseFormValues["items"];
  remove: UseFieldArrayRemove;
  handleProductChange: (index: number, productId: string) => void;
  isPending: boolean;
  canRemove: boolean;
}

const CustomDropdownProductSelect: React.FC<
  CustomDropdownProductSelectProps
> = ({
  control,
  index,
  field: _, // Unused but required by useFieldArray
  products,
  items,
  remove,
  handleProductChange,
  isPending,
  canRemove,
}) => {
  const form = useFormContext<PurchaseFormValues>();
  const item = items[index];
  const quantity = item?.quantity || 0;
  const cost = item?.costAtPurchase || 0;
  const discountPercentage = item?.discountPercentage || 0;
  const discountAmount = item?.discountAmount || 0;
  const taxRate = parseFloat(item?.tax || "0") / 100;
  const priceIncludesTax = form.watch("priceIncludesTax");

  // Calculate discount
  let finalCost = cost;
  if (discountPercentage > 0) {
    finalCost = cost * (1 - discountPercentage / 100);
  } else if (discountAmount > 0) {
    finalCost = Math.max(0, cost - discountAmount);
  }

  // Calculate subtotal based on whether price includes tax
  let subtotal;

  if (priceIncludesTax) {
    // If price includes tax, the subtotal is simply quantity * finalCost
    subtotal = quantity * finalCost;
  } else {
    // If price doesn't include tax, we add tax to the price
    const subtotalBeforeTax = quantity * finalCost;
    const taxAmount = subtotalBeforeTax * taxRate;
    subtotal = subtotalBeforeTax + taxAmount;
  }

  return (
    <tr className="border-b border-gray-200 dark:border-gray-700 h-20">
      {/* Product Selection */}
      <td className="py-4 pl-0 pr-2" style={{ minWidth: "300px" }}>
        <FormField
          control={control}
          name={`items.${index}.productId`}
          render={({ field: formField }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <ProductDropdown
                    value={formField.value}
                    onChange={(value) => {
                      formField.onChange(value);
                      handleProductChange(index, value);
                    }}
                    products={products}
                    disabled={isPending}
                  />
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </td>

      {/* Quantity */}
      <td className="py-4 px-2" style={{ minWidth: "100px" }}>
        <FormField
          control={control}
          name={`items.${index}.quantity`}
          render={({ field: formField }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <Input
                    type="number"
                    min="1"
                    step="1"
                    {...formField}
                    onChange={(e) => {
                      const value = parseInt(e.target.value);
                      formField.onChange(value || 1);

                      // Force recalculation of total immediately
                      const currentItems = form.getValues("items");

                      // Update the current item's quantity
                      if (currentItems[index]) {
                        currentItems[index].quantity = value || 1;
                      }

                      // Recalculate the total with discount and tax
                      const priceIncludesTax = form.watch("priceIncludesTax");
                      const total = currentItems.reduce(
                        (sum: number, item: any) => {
                          const quantity = item?.quantity ?? 0;
                          const cost = item?.costAtPurchase ?? 0;
                          const discountPercentage =
                            item?.discountPercentage ?? 0;
                          const discountAmount = item?.discountAmount ?? 0;
                          const taxRate = parseFloat(item?.tax || "0") / 100;

                          // Calculate discount
                          let finalCost = cost;
                          if (discountPercentage > 0) {
                            finalCost = cost * (1 - discountPercentage / 100);
                          } else if (discountAmount > 0) {
                            finalCost = Math.max(0, cost - discountAmount);
                          }

                          let itemSubtotal;
                          if (priceIncludesTax) {
                            // If price includes tax, the total is simply quantity * finalCost
                            itemSubtotal = quantity * finalCost;
                          } else {
                            // If price doesn't include tax, we add tax to the finalCost
                            const subtotalBeforeTax = quantity * finalCost;
                            const taxAmount = subtotalBeforeTax * taxRate;
                            itemSubtotal = subtotalBeforeTax + taxAmount;
                          }

                          return sum + itemSubtotal;
                        },
                        0
                      );

                      // Update the total in the form
                      form.setValue("totalAmount", total);
                    }}
                    disabled={isPending}
                    className="w-full"
                  />
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </td>

      {/* Unit */}
      <td className="py-4 px-2" style={{ minWidth: "100px" }}>
        <FormField
          control={control}
          name={`items.${index}.unit`}
          render={({ field: formField }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <Input
                    type="text"
                    {...formField}
                    disabled={true}
                    className="bg-gray-100 dark:bg-gray-800 cursor-not-allowed"
                  />
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </td>

      {/* Cost */}
      <td className="py-4 px-2" style={{ minWidth: "150px" }}>
        <FormField
          control={control}
          name={`items.${index}.costAtPurchase`}
          render={({ field: formField }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <span className="text-gray-500 dark:text-gray-400">
                        Rp
                      </span>
                    </div>
                    <Input
                      type="text"
                      value={formatCurrency(formField.value, false)}
                      onChange={(e) => {
                        // Remove all non-numeric characters (including dots)
                        const value = e.target.value.replace(/[^0-9]/g, "");

                        // Parse the sanitized value as a number for the form state
                        const numericValue = parseInt(value);

                        // Update the form field with the numeric value
                        formField.onChange(
                          isNaN(numericValue) ? 0 : numericValue
                        );

                        // Force recalculation of total immediately
                        const currentItems = form.getValues("items");

                        // Update the current item's costAtPurchase
                        if (currentItems[index]) {
                          currentItems[index].costAtPurchase = isNaN(
                            numericValue
                          )
                            ? 0
                            : numericValue;
                        }

                        // Recalculate the total with discount and tax
                        const priceIncludesTax = form.watch("priceIncludesTax");
                        const total = currentItems.reduce(
                          (sum: number, item: any) => {
                            const quantity = item?.quantity ?? 0;
                            const cost = item?.costAtPurchase ?? 0;
                            const discountPercentage =
                              item?.discountPercentage ?? 0;
                            const discountAmount = item?.discountAmount ?? 0;
                            const taxRate = parseFloat(item?.tax || "0") / 100;

                            // Calculate discount
                            let finalCost = cost;
                            if (discountPercentage > 0) {
                              finalCost = cost * (1 - discountPercentage / 100);
                            } else if (discountAmount > 0) {
                              finalCost = Math.max(0, cost - discountAmount);
                            }

                            let itemSubtotal;
                            if (priceIncludesTax) {
                              // If price includes tax, the total is simply quantity * finalCost
                              itemSubtotal = quantity * finalCost;
                            } else {
                              // If price doesn't include tax, we add tax to the finalCost
                              const subtotalBeforeTax = quantity * finalCost;
                              const taxAmount = subtotalBeforeTax * taxRate;
                              itemSubtotal = subtotalBeforeTax + taxAmount;
                            }

                            return sum + itemSubtotal;
                          },
                          0
                        );

                        // Update the total in the form
                        form.setValue("totalAmount", total);
                      }}
                      disabled={isPending}
                      className="pl-10 w-full"
                    />
                  </div>
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </td>

      {/* Discount */}
      <td className="py-4 px-2" style={{ minWidth: "120px" }}>
        <DiscountField
          control={control}
          index={index}
          isPending={isPending}
          form={form}
        />
      </td>

      {/* Tax */}
      <td className="py-4 px-2" style={{ minWidth: "100px" }}>
        <FormField
          control={control}
          name={`items.${index}.tax`}
          render={({ field: formField }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <div className="relative">
                    <Input
                      type="text"
                      className="pr-6"
                      placeholder="0"
                      {...formField}
                      disabled={isPending}
                      onChange={(e) => {
                        // Only allow numbers and decimal point
                        const value = e.target.value.replace(/[^0-9.]/g, "");
                        formField.onChange(value);

                        // Force recalculation of total immediately
                        const currentItems = form.getValues("items");

                        // Update the current item's tax
                        if (currentItems[index]) {
                          currentItems[index].tax = value;
                        }

                        // Recalculate the total with discount and tax
                        const priceIncludesTax = form.watch("priceIncludesTax");
                        const total = currentItems.reduce(
                          (sum: number, item: any) => {
                            const quantity = item?.quantity ?? 0;
                            const cost = item?.costAtPurchase ?? 0;
                            const discountPercentage =
                              item?.discountPercentage ?? 0;
                            const discountAmount = item?.discountAmount ?? 0;
                            const taxRate = parseFloat(item?.tax || "0") / 100;

                            // Calculate discount
                            let finalCost = cost;
                            if (discountPercentage > 0) {
                              finalCost = cost * (1 - discountPercentage / 100);
                            } else if (discountAmount > 0) {
                              finalCost = Math.max(0, cost - discountAmount);
                            }

                            let itemSubtotal;
                            if (priceIncludesTax) {
                              // If price includes tax, the total is simply quantity * finalCost
                              itemSubtotal = quantity * finalCost;
                            } else {
                              // If price doesn't include tax, we add tax to the finalCost
                              const subtotalBeforeTax = quantity * finalCost;
                              const taxAmount = subtotalBeforeTax * taxRate;
                              itemSubtotal = subtotalBeforeTax + taxAmount;
                            }

                            return sum + itemSubtotal;
                          },
                          0
                        );

                        // Update the total in the form
                        form.setValue("totalAmount", total);
                      }}
                    />
                    <span className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 pointer-events-none">
                      %
                    </span>
                  </div>
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </td>

      {/* Subtotal (calculated) */}
      <td className="py-4 px-2" style={{ minWidth: "120px" }}>
        <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {formatCurrency(subtotal)}
        </div>
      </td>

      {/* Remove Button */}
      <td className="py-4 px-0 text-right" style={{ minWidth: "70px" }}>
        {canRemove && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => remove(index)}
            disabled={isPending}
            className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 cursor-pointer"
          >
            <TrashIcon className="h-5 w-5" />
          </Button>
        )}
      </td>
    </tr>
  );
};

// Discount Field Component with Type Selector
interface DiscountFieldProps {
  control: Control<PurchaseFormValues>;
  index: number;
  isPending: boolean;
  form: any;
}

const DiscountField: React.FC<DiscountFieldProps> = ({
  control,
  index,
  isPending,
  form,
}) => {
  const [discountType, setDiscountType] = useState<"percentage" | "amount">(
    "percentage"
  );

  const handleDiscountChange = (
    value: string,
    type: "percentage" | "amount"
  ) => {
    const numericValue = parseFloat(value) || 0;

    if (type === "percentage") {
      form.setValue(`items.${index}.discountPercentage`, numericValue);
      form.setValue(`items.${index}.discountAmount`, 0);
    } else {
      form.setValue(`items.${index}.discountAmount`, numericValue);
      form.setValue(`items.${index}.discountPercentage`, 0);
    }

    // Recalculate total
    recalculateTotal(form, index);
  };

  const recalculateTotal = (form: any, currentIndex: number) => {
    const currentItems = form.getValues("items");
    const priceIncludesTax = form.watch("priceIncludesTax");

    const total = currentItems.reduce(
      (sum: number, item: any, itemIndex: number) => {
        const quantity = item?.quantity ?? 0;
        const cost = item?.costAtPurchase ?? 0;
        const discountPercentage = item?.discountPercentage ?? 0;
        const discountAmount = item?.discountAmount ?? 0;
        const taxRate = parseFloat(item?.tax || "0") / 100;

        // Calculate discount
        let finalCost = cost;
        if (discountPercentage > 0) {
          finalCost = cost * (1 - discountPercentage / 100);
        } else if (discountAmount > 0) {
          finalCost = Math.max(0, cost - discountAmount);
        }

        let itemSubtotal;
        if (priceIncludesTax) {
          itemSubtotal = quantity * finalCost;
        } else {
          const subtotalBeforeTax = quantity * finalCost;
          const taxAmount = subtotalBeforeTax * taxRate;
          itemSubtotal = subtotalBeforeTax + taxAmount;
        }

        return sum + itemSubtotal;
      },
      0
    );

    form.setValue("totalAmount", total);
  };

  const currentItem = form.watch(`items.${index}`);
  const discountPercentage = currentItem?.discountPercentage ?? 0;
  const discountAmount = currentItem?.discountAmount ?? 0;

  // Determine current discount type and value
  const currentDiscountType = discountPercentage > 0 ? "percentage" : "amount";
  const currentValue =
    discountPercentage > 0 ? discountPercentage : discountAmount;

  return (
    <div className="flex items-center space-x-1">
      <div className="flex-1">
        <FormField
          control={control}
          name={
            discountType === "percentage"
              ? `items.${index}.discountPercentage`
              : `items.${index}.discountAmount`
          }
          render={({ field: formField }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <Input
                    type="text"
                    placeholder="0.0"
                    value={currentValue.toString()}
                    onChange={(e) => {
                      const value = e.target.value.replace(/[^0-9.]/g, "");
                      handleDiscountChange(value, discountType);
                    }}
                    disabled={isPending}
                    className="w-full pr-8"
                  />
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </div>
      <Select
        value={discountType}
        onValueChange={(value: "percentage" | "amount") => {
          setDiscountType(value);
          // Reset both discount values when switching type
          form.setValue(`items.${index}.discountPercentage`, 0);
          form.setValue(`items.${index}.discountAmount`, 0);
          recalculateTotal(form, index);
        }}
        disabled={isPending}
      >
        <SelectTrigger className="w-12 h-8 p-1">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="percentage">%</SelectItem>
          <SelectItem value="amount">Rp</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};

// Custom Product Dropdown Component with Images and Search
interface ProductDropdownProps {
  value: string;
  onChange: (value: string) => void;
  products: Product[];
  disabled?: boolean;
}

const ProductDropdown: React.FC<ProductDropdownProps> = ({
  value,
  onChange,
  products,
  disabled = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const selectedProduct = products.find((product) => product.id === value);

  // Handle mounting for client-side rendering
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Close dropdown when pressing escape
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setIsOpen(false);
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => {
      document.removeEventListener("keydown", handleEscape);
    };
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Update position when dropdown is opened, scrolled, or window is resized
  useEffect(() => {
    const updatePosition = () => {
      if (isOpen && dropdownRef.current) {
        const rect = dropdownRef.current.getBoundingClientRect();
        const dropdownWidth = 300; // Width of dropdown in pixels

        // Check if dropdown would go off the right edge of the screen
        const rightEdge = rect.left + dropdownWidth;
        const windowWidth = window.innerWidth;

        // If it would go off-screen, adjust the left position
        const adjustedLeft =
          rightEdge > windowWidth
            ? Math.max(0, windowWidth - dropdownWidth)
            : rect.left;

        // Calculate position relative to the viewport (for absolute positioning)
        setPosition({
          top: rect.bottom + window.scrollY + 5,
          left: adjustedLeft,
        });
      }
    };

    updatePosition();

    // Add event listeners for resize and scroll
    window.addEventListener("resize", updatePosition);
    window.addEventListener("scroll", updatePosition, true);

    return () => {
      window.removeEventListener("resize", updatePosition);
      window.removeEventListener("scroll", updatePosition, true);
    };
  }, [isOpen]);

  // Search products from API
  const searchProducts = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);
    try {
      const response = await fetch(
        `/api/products/search?q=${encodeURIComponent(query)}&limit=10`
      );
      if (!response.ok) throw new Error("Failed to search products");

      const data = await response.json();
      setSearchResults(data.products || []);
    } catch (error) {
      console.error("Error searching products:", error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      searchProducts(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Get products to display - either search results or 3 newest products
  const displayProducts = searchTerm
    ? searchResults
    : [...products]
        .sort((a, b) => {
          // Sort by createdAt if available, otherwise use id as fallback
          if (a.createdAt && b.createdAt) {
            const dateA =
              a.createdAt instanceof Date ? a.createdAt : new Date(a.createdAt);
            const dateB =
              b.createdAt instanceof Date ? b.createdAt : new Date(b.createdAt);
            return dateB.getTime() - dateA.getTime();
          }
          // Fallback to id comparison if createdAt is not available
          return a.id > b.id ? -1 : 1; // Newer IDs are typically longer/greater
        })
        .slice(0, 3);

  return (
    <div className="relative" ref={dropdownRef}>
      <div
        className={`flex items-center justify-between w-full rounded-md border ${
          disabled
            ? "bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700"
            : "bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
        } py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm text-gray-900 dark:text-gray-100 cursor-pointer`}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        <div className="flex items-center gap-3 overflow-hidden">
          {selectedProduct?.image ? (
            <div className="relative h-7 w-7 rounded-sm overflow-hidden">
              <Image
                src={selectedProduct.image}
                alt={selectedProduct.name}
                fill
                sizes="28px"
                className="object-cover"
                unoptimized
              />
            </div>
          ) : selectedProduct ? (
            <div className="h-7 w-7 bg-gray-200 dark:bg-gray-600 rounded-sm flex items-center justify-center">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                No img
              </span>
            </div>
          ) : (
            <div className="h-7 w-7 bg-gray-100 dark:bg-gray-700 rounded-sm flex items-center justify-center">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                Img
              </span>
            </div>
          )}
          <span className="truncate font-medium">
            {selectedProduct ? selectedProduct.name : "Pilih Produk"}
          </span>
        </div>
        {!disabled &&
          (isOpen ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          ))}
      </div>

      {mounted &&
        isOpen &&
        !disabled &&
        createPortal(
          <>
            <div
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            <div
              className="absolute z-50 w-[300px] rounded-md bg-white dark:bg-gray-800 shadow-xl max-h-[300px] overflow-auto border border-gray-200 dark:border-gray-700"
              style={{
                top: position.top,
                left: position.left,
                maxWidth: "calc(100vw - 20px)",
              }}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="py-1">
                {/* Search input */}
                <div className="px-3 py-2 border-b border-gray-200 dark:border-gray-700">
                  <div className="relative">
                    <input
                      ref={searchInputRef}
                      type="text"
                      placeholder="Cari produk..."
                      className="w-full px-3 py-1.5 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm text-gray-900 dark:text-gray-100 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </div>
                </div>

                {/* Default option */}
                <div
                  className="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-700"
                  onClick={() => {
                    onChange("");
                    setIsOpen(false);
                  }}
                >
                  Pilih Produk
                </div>

                {/* Loading indicator */}
                {isSearching && (
                  <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                    Mencari...
                  </div>
                )}

                {/* No results message */}
                {!isSearching && searchTerm && displayProducts.length === 0 && (
                  <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                    Tidak ada produk ditemukan
                  </div>
                )}

                {/* Product list */}
                {displayProducts.map((product) => (
                  <div
                    key={product.id}
                    className="px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                    onClick={() => {
                      onChange(product.id);
                      setIsOpen(false);
                    }}
                  >
                    <div className="flex items-center gap-3">
                      {product.image ? (
                        <div className="relative h-8 w-8 rounded-sm overflow-hidden">
                          <Image
                            src={product.image}
                            alt={product.name}
                            fill
                            sizes="32px"
                            className="object-cover"
                            unoptimized
                          />
                        </div>
                      ) : (
                        <div className="h-8 w-8 bg-gray-200 dark:bg-gray-600 rounded-sm flex items-center justify-center">
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            No img
                          </span>
                        </div>
                      )}
                      <span className="truncate">{product.name}</span>
                    </div>
                  </div>
                ))}

                {/* Add Product Link */}
                <Link
                  href="/dashboard/products/new"
                  className="mt-2 px-3 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-t border-gray-200 dark:border-gray-700 flex items-center gap-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsOpen(false);
                  }}
                >
                  <Plus className="h-4 w-4" />
                  <span>Tambah Produk</span>
                </Link>
              </div>
            </div>
          </>,
          document.body
        )}
    </div>
  );
};

export default CustomDropdownProductSelect;
